import { VStack, Text } from "@chakra-ui/react";
import FormCombobox from "@/components/global/combobox/form-combobox";
import { SearchMode, SearchModeOption } from "../data/types";

interface SearchModeSelectorProps {
  selectedMode: SearchMode;
  onModeChange: (mode: SearchMode) => void;
}

const searchModeOptions: SearchModeOption[] = [
  { value: "segments", label: "Segmentos/Cargos" },
  { value: "respondent", label: "Respondente" },
];

export function SearchModeSelector({
  selectedMode,
  onModeChange,
}: SearchModeSelectorProps) {
  return (
    <VStack align="stretch" gap={2} w={{ base: "200px", lg: "250px" }}>
      <FormCombobox
        options={searchModeOptions}
        value={selectedMode}
        onValueChange={(value) => onModeChange(value as SearchMode)}
        placeholder="Modo de busca"
        label=""
      />
    </VStack>
  );
}
