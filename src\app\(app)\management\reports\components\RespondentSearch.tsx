import { VStack, Text, Box, Flex, Alert } from "@chakra-ui/react";
import { GetRespondentsBySearchDto } from "@/utils/types/DTO/respondents/respondents.dto";
import { HierarchicalCheckboxCard } from "./HierarchicalCheckboxCard";

interface RespondentSearchProps {
  selectedRespondent: string | null;
  onRespondentChange: (respondentId: string | null) => void;
  respondentsData: GetRespondentsBySearchDto | undefined;
  searchTerm: string;
  isLoading: boolean;
}

export function RespondentSearch({
  selectedRespondent,
  onRespondentChange,
  respondentsData,
  searchTerm,
  isLoading,
}: RespondentSearchProps) {
  const handleRespondentSelect = (respondentId: string, checked: boolean) => {
    if (checked) {
      onRespondentChange(respondentId);
    } else {
      onRespondentChange(null);
    }
  };

  return (
    <Flex
      flex={1}
      gap={6}
      w={"100%"}
      alignItems="center"
      flexDirection="column"
      overflowX="auto"
      justify="center"
    >
      <Text
        fontSize={{ base: "20px", lg: "30px" }}
        color="white"
        alignSelf="center"
      >
        Buscar Respondente
      </Text>

      <VStack align="stretch" gap={4} w="100%" maxW="600px">
        {!searchTerm.trim() && !isLoading && (
          <Alert.Root status="info" variant="subtle">
            <Alert.Indicator />
            <Alert.Content>
              <Alert.Title>Digite um termo de busca</Alert.Title>
              <Alert.Description>
                Use o campo de busca no cabeçalho para encontrar respondentes.
              </Alert.Description>
            </Alert.Content>
          </Alert.Root>
        )}

        {searchTerm.trim() &&
          !isLoading &&
          respondentsData &&
          respondentsData.length === 0 && (
            <Alert.Root status="warning" variant="subtle">
              <Alert.Indicator />
              <Alert.Content>
                <Alert.Title>Nenhum respondente encontrado</Alert.Title>
                <Alert.Description>
                  Não foram encontrados respondentes com o termo "{searchTerm}".
                </Alert.Description>
              </Alert.Content>
            </Alert.Root>
          )}

        {respondentsData && respondentsData.length > 0 && (
          <VStack align="stretch" gap={3} maxH="400px" overflowY="auto">
            {respondentsData.map((respondent) => (
              <Box key={respondent.secureId} w="100%">
                <HierarchicalCheckboxCard
                  id={respondent.secureId}
                  label={respondent.name}
                  isChecked={selectedRespondent === respondent.secureId}
                  onChange={(_, checked) =>
                    handleRespondentSelect(respondent.secureId, checked)
                  }
                  level="segment"
                />
              </Box>
            ))}
          </VStack>
        )}
      </VStack>
    </Flex>
  );
}
