import { Box, CheckboxCard, VStack } from "@chakra-ui/react";

type HierarchicalCheckboxCardProps = {
  id: string;
  label: string;
  isChecked: boolean;
  onChange: (id: string, checked: boolean) => void;
  level: "segment" | "superintendencia" | "gerencia" | "cargo";
  children?: React.ReactNode;
};

type HierarchyLevel = "segment" | "superintendencia" | "gerencia" | "cargo";

export function HierarchicalCheckboxCard({
  id,
  label,
  isChecked,
  onChange,
  level,
  children,
}: HierarchicalCheckboxCardProps) {
  const handleChange = (checked: boolean) => {
    onChange(id, checked);
  };

  const getFontSize = (level: HierarchyLevel) => {
    switch (level) {
      case "segment":
        return {base: "xl", lg: "2xl"};
      case "superintendencia":
        return {base: "lg", lg: "xl"};
      case "gerencia":
        return {base: "md", lg: "lg"};
      case "cargo":
        return {base: "sm", lg: "md"};
    }
  };

  const getMarginLeft = (level: HierarchyLevel) => {
    switch (level) {
      case "segment":
        return 0;
      case "superintendencia":
        return 4;
      case "gerencia":
        return 8;
      case "cargo":
        return 12;
    }
  };

  const getIndicatorSize = (level: HierarchyLevel) => {
    switch (level) {
      case "segment":
        return 10;
      default:
        return 8;
    }
  };

  return (
    <VStack align="stretch" gap={2}>
      <CheckboxCard.Root
        variant={"surface"}
        borderRadius="full"
        border="1px solid"
        borderColor={isChecked ? "#a6864a" : "gray.600"}
        bg={"transparent"}
        align="center"
        ml={getMarginLeft(level)}
        mb={2}
        outline={"none"}
      >
        <CheckboxCard.HiddenInput
          checked={isChecked}
          onChange={(e) => handleChange(e.target.checked)}
        />
        <CheckboxCard.Control p={0} pr={0}>
          <CheckboxCard.Indicator
            w={getIndicatorSize(level)}
            h={getIndicatorSize(level)}
            bgColor={isChecked ? "#a6864a" : "gray.600"}
            color={"transparent"}
            borderRadius="2xl"
            borderColor={isChecked ? "#a6864a" : "gray.600"}
          />
          <CheckboxCard.Label fontSize={getFontSize(level)} color={"white"}>
            {label}
          </CheckboxCard.Label>
        </CheckboxCard.Control>
      </CheckboxCard.Root>
      {children && <Box>{children}</Box>}
    </VStack>
  );
}
