"use client";
import Button from "@/components/global/buttons/button";
import {
  Box,
  HStack,
  Text,
  VStack,
  Image,
  AspectRatio,
} from "@chakra-ui/react";

type DefaultScreenProps = {
  userName?: string;
  topText: React.ReactNode;
  videoSrc: string;
  bottomText: React.ReactNode;
  buttonText: string;
  isInverted?: boolean;
  onButtonClick?: () => void;
};

export default function DefaultScreen({
  bottomText,
  buttonText,
  topText,
  videoSrc,
  isInverted,
  userName,
  onButtonClick,
}: DefaultScreenProps) {
  return (
    <HStack
      flex={1}
      h={"100%"}
      justifyContent="center"
      alignItems="center"
      gap={0}
      direction={{ base: "column", lg: "row" }}
    >
      <VStack 
        w={{ base: "100%", lg: "50%" }} 
        h={"100%"}
        order={{ base: 1, lg: 0 }}
      >
        <VStack
          flex={1}
          bgColor={isInverted ? "rgb(35,34,34)" : "rgb(255,255,255)"}
          overflow={{ base: "auto", lg: "visible" }}
        >
          <VStack
            h={{ base: "auto", lg: "50%" }}
            w={"100%"}
            justifyContent={{ base: "start", lg: "start" }}
            alignItems="center"
            px={{ base: 6, lg: 20 }}
            pt={{ base: 4, lg: 0 }}
            gap={3}
          >
            <HStack
              w={"100%"}
              h={{ base: "auto", lg: "50%" }}
              justifyContent={"start"}
              alignItems="center"
              gap={10}
            >
              <Image
                src="/images/logoBancoABC.svg"
                alt="Banco ABC"
                w={{ base: "70px", lg: "100px" }}
                h="auto"
                filter={
                  isInverted
                    ? ""
                    : "brightness(0) saturate(100%) invert(11%) sepia(4%) saturate(0%) hue-rotate(314deg) brightness(96%) contrast(88%)"
                }
              />
              {userName && (
                <Text
                  color={isInverted ? "white" : "rgb(32,32,31)"}
                  fontSize={{ base: "lg", lg: "3xl" }}
                  fontWeight="bold"
                >
                  Bem-Vindo (a), <br /> {userName}.
                </Text>
              )}
            </HStack>
            <Text
              fontSize={{ base: "xl", lg: 32 }}
              fontWeight="light"
              color={isInverted ? "white" : "rgb(32,32,31)"}
              display={{ base: "block", lg: "block" }}
            >
              {topText}
            </Text>
          </VStack>
          
          <Box
            w={"100%"}
            h={{ base: "300px", lg: "0" }}
            borderTopLeftRadius={60}
            overflow="hidden"
            pl={{ base: 10, lg: 0 }}
            display={{ base: "block", lg: "none" }}
          >
            <AspectRatio ratio={16 / 9} w={"100%"} h={"100%"}>
              <iframe
                src={videoSrc}
                width="100%"
                height="100%"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                title="Video Assessment"
                style={{
                  borderRadius: "60px 0 0 60px",
                  border: "transparent",
                }}
              />
            </AspectRatio>
          </Box>

          <VStack
            h={{ base: "auto", lg: "50%" }}
            flex={{ base: 1, lg: 0 }}
            bgColor={isInverted ? "rgb(255,255,255)" : "rgb(35,34,34)"}
            borderTopEndRadius={{ base: 0, lg: 60 }}
            p={{ base: 6, lg: 20 }}
            gap={8}
            justify={"center"}
            align="start"
            w="100%"
          >
            <Text
              fontSize={{ base: "xs", lg: 24 }}
              fontWeight="medium"
              color={isInverted ? "rgb(35,34,34)" : "white"}
            >
              {bottomText}
            </Text>
            <Button
              w={{ base: 32, lg: 40 }}
              h={{ base: 12, lg: 14 }}
              borderRadius={20}
              fontSize={{ base: 16, lg: 24 }}
              fontWeight={"extrabold"}
              onClick={onButtonClick}
            >
              {buttonText}
            </Button>
          </VStack>
        </VStack>
      </VStack>
      
      <Box
        w={{ base: "0", lg: "50%" }}
        h={{ base: "0", lg: "100%" }}
        bgColor={isInverted ? "rgb(35,34,34)" : "rgb(255,255,255)"}
        display={{ base: "none", lg: "block" }}
      >
        <Box
          w={"100%"}
          h={"100%"}
          borderTopLeftRadius={60}
          bgColor={isInverted ? "rgb(255,255,255)" : "rgb(35,34,34)"}
          overflow="hidden"
        >
          <iframe
            src={videoSrc}
            width="100%"
            height="100%"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            title="Video Assessment"
            style={{
              borderRadius: "60px 0 0 60px",
              border: "transparent",
            }}
          />
        </Box>
      </Box>
    </HStack>
  );
}
