import { Flex, Text, VStack, Wrap, WrapItem } from "@chakra-ui/react";
import { Segment } from "../data/types";
import { HierarchicalCheckboxCard } from "./HierarchicalCheckboxCard";

interface SegmentDisplayProps {
  segments: Segment[];
  onItemChangeByPath: (
    segmentId: string,
    superintendenciaId?: string,
    gerenciaId?: string,
    cargoId?: string,
    checked?: boolean
  ) => void;
  isItemSelectedByPath: (
    segmentId: string,
    superintendenciaId?: string,
    gerenciaId?: string,
    cargoId?: string
  ) => boolean;
}

export function SegmentDisplay({
  segments,
  onItemChangeByPath,
  isItemSelectedByPath,
}: SegmentDisplayProps) {
  return (
    <Flex
      flex={1}
      gap={0}
      w={"100%"}
      alignItems={"center"}
      flexDirection={"column"}
      overflowX="auto"
      justify="center"
    >
      <Text
        fontSize={{ base: "20px", lg: "30px" }}
        color="white"
        alignSelf={"center"}
        marginBottom={6}
      >
        Escolha o Segmento e Cargo
      </Text>

      <Wrap gap={8} justify="center" align="flex-start">
        {segments?.map((segment) => (
          <WrapItem key={segment.secureId}>
            <VStack align="stretch" minW={{ base: "320px", lg: "400px" }}>
              <HierarchicalCheckboxCard
                id={segment.secureId}
                label={segment.segmento_nome}
                isChecked={isItemSelectedByPath(segment.secureId)}
                onChange={(_, checked) =>
                  onItemChangeByPath(
                    segment.secureId,
                    undefined,
                    undefined,
                    undefined,
                    checked
                  )
                }
                level="segment"
              >
                {segment.superintendencias.map((superintendencia) => (
                  <HierarchicalCheckboxCard
                    key={superintendencia.secureId}
                    id={superintendencia.secureId}
                    label={superintendencia.superintendencia_nome}
                    isChecked={isItemSelectedByPath(
                      segment.secureId,
                      superintendencia.secureId
                    )}
                    onChange={(_, checked) =>
                      onItemChangeByPath(
                        segment.secureId,
                        superintendencia.secureId,
                        undefined,
                        undefined,
                        checked
                      )
                    }
                    level="superintendencia"
                  >
                    {superintendencia.gerencias.map((gerencia) => (
                      <HierarchicalCheckboxCard
                        key={gerencia.secureId}
                        id={gerencia.secureId}
                        label={gerencia.gerencia_nome}
                        isChecked={isItemSelectedByPath(
                          segment.secureId,
                          superintendencia.secureId,
                          gerencia.secureId
                        )}
                        onChange={(_, checked) =>
                          onItemChangeByPath(
                            segment.secureId,
                            superintendencia.secureId,
                            gerencia.secureId,
                            undefined,
                            checked
                          )
                        }
                        level="gerencia"
                      >
                        {gerencia.cargos.map((cargo) => (
                          <HierarchicalCheckboxCard
                            key={cargo.secureId}
                            id={cargo.secureId}
                            label={cargo.cargo_nome}
                            isChecked={isItemSelectedByPath(
                              segment.secureId,
                              superintendencia.secureId,
                              gerencia.secureId,
                              cargo.secureId
                            )}
                            onChange={(_, checked) =>
                              onItemChangeByPath(
                                segment.secureId,
                                superintendencia.secureId,
                                gerencia.secureId,
                                cargo.secureId,
                                checked
                              )
                            }
                            level="cargo"
                          />
                        ))}
                      </HierarchicalCheckboxCard>
                    ))}
                  </HierarchicalCheckboxCard>
                ))}
              </HierarchicalCheckboxCard>
            </VStack>
          </WrapItem>
        ))}
      </Wrap>
    </Flex>
  );
}
