import {
  HStack,
  Image,
  Input,
  InputGroup,
  Text,
  But<PERSON>,
  Stack,
} from "@chakra-ui/react";
import { LuSearch } from "react-icons/lu";
import { SearchModeSelector } from "./SearchModeSelector";
import { SearchMode } from "../data/types";
import DefaultButton from "@/components/global/buttons/button";

interface ReportsHeaderProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onGenerateReport: () => void;
  isGenerateDisabled: boolean;
  isGenerating: boolean;
  searchMode: SearchMode;
  onSearchModeChange: (mode: SearchMode) => void;
}

export function ReportsHeader({
  searchTerm,
  onSearchChange,
  onGenerateReport,
  isGenerateDisabled,
  isGenerating,
  searchMode,
  onSearchModeChange,
}: ReportsHeaderProps) {
  return (
    <Stack
      w="100%"
      justifyContent={"space-between"}
      gap={{ base: 4, lg: 0 }}
      mb={8}
      flexDirection={{ base: "column", lg: "row" }}
    >
      <HStack
        justifyContent={{ base: "none", lg: "space-between" }}
        gap={{ base: 6, lg: 32 }}
        w={{ base: "none", lg: "50%" }}
      >
        <Image
          src="/images/logoBancoABC.svg"
          alt="Banco ABC"
          w={{ base: "70px", lg: "100px" }}
          h="auto"
        />
        <Text fontSize={{ base: "20px", lg: "50px" }}>Filtro Visão Banco</Text>
      </HStack>
      <Stack
        gap={4}
        w="50%"
        flex={1}
        flexDirection={{ base: "column", lg: "row" }}
        align={"center"}
        justifyContent={{ base: "flex-start", lg: "flex-end" }}
      >
        {/* Search Mode Selector */}
        <SearchModeSelector
          selectedMode={searchMode}
          onModeChange={onSearchModeChange}
        />
        <DefaultButton
          onClick={onGenerateReport}
          disabled={isGenerateDisabled}
          loading={isGenerating}
          bg="#a6864a"
          color="white"
          _disabled={{
            bg: "gray.400",
            cursor: "not-allowed",
          }}
          size="lg"
        >
          Gerar Relatório
        </DefaultButton>

        {/* Conditional Search Input - Only show for respondent mode */}
        {searchMode === "respondent" && (
          <InputGroup startElement={<LuSearch />} maxW="400px">
            <Input
              placeholder="Buscar por nome do respondente"
              bg="white"
              border="1px solid"
              borderColor="gray.600"
              color="black"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
          </InputGroup>
        )}
      </Stack>
    </Stack>
  );
}
