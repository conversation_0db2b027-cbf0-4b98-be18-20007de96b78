"use client";
import {
  Box,
  Flex,
  Grid,
  Grid<PERSON>tem,
  HStack,
  Image,
  Text,
  VStack,
} from "@chakra-ui/react";

export default function Management() {
  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} mt={{ base: 8, lg: 0 }}>
      <Box
        position="absolute"
        bottom={{ base: 0, lg: "auto" }}
        top={{ base: 10, lg: 0 }}
        left={{ base: "15rem", lg: 0 }}
        w={"100%"}
        h={{ base: "100vh", lg: "100%" }}
        transform={{ base: "scale(1.5)", lg: "translateY(50%)" }}
        bgImg="url(/images/login/padraoBG.svg)"
        bgRepeat="no-repeat"
        bgPos={{ base: "center left", lg: "center bottom" }}
        bgSize="contain"
        zIndex={{ base: 0, lg: "auto" }}
        clipPath={{ base: "none", lg: "inset(0 0 50% 0)" }}
      />
      
      <Grid
        templateColumns={{ base: "repeat(1, 2fr)", lg: "repeat(2, 1fr)" }}
        gap={2}
        w={"100vw"}
        h="100vh"
        position={"relative"}
      >
        <GridItem>
          <VStack 
            height={{ base: "100vw", lg: "100vh" }} 
            p={8} 
            gap={{base: 2, lg: 8}} 
            alignItems="center"
          >
            <HStack
              w={{ base: "100vw", lg: "100%" }}
              justifyContent={{ base: "start", lg: "center" }}
              alignItems="center"
              gap={{ base: 2, lg: 8 }}
            >
              <Image
                src="/images/logoBancoABC.svg"
                alt="Banco ABC"
                w={{ base: "40px", lg: "100px" }}
                h="auto"
              />
              <Text fontSize={{ base: "md", lg: "40px" }}>
                Dashboard Assessment ABC BRASIL
              </Text>
            </HStack>
            
            <Flex
              as="main"
              w="100%"
              ml={{ base: 0, lg: "auto" }}
              flex={1}
              alignItems={{ base: "start", lg: "center" }}
              justifyContent={{ base: "start", lg: "center" }}
            >
              <VStack 
                gap={{ base: 2, lg: 6 }} 
                alignItems="flex-start" 
                maxW={{ base: "300px", lg: "570px" }} 
              >
                <Text
                  fontSize={{ base: "xl", lg: "32px" }}
                  letterSpacing="wider"
                  color="#a6864a"
                  fontWeight="bold"
                >
                  INTRODUÇÃO
                </Text>
                
                <VStack gap={2} alignItems="flex-start" w="full">
                  <Text 
                    fontSize={{ base: "xs", lg: "md" }} 
                    textAlign={{ base: "start", lg: "justify" }} 
                    lineHeight="1.6"
                  >
                    Aqui você encontra sua proficiência nas 36 competências essenciais para um gerente de relacionamento do ABC Brasil, com base em aspectos técnicos, estratégicos e relacionais.
                  </Text>
                  
                  <Text 
                    fontSize={{ base: "xs", lg: "md" }} 
                    textAlign={{ base: "start", lg: "justify" }} 
                    lineHeight="1.6"
                  >
                    O objetivo é oferecer clareza sobre seus pontos fortes e oportunidades de desenvolvimento, apoiando uma trilha de aprendizado personalizada.
                  </Text>
                  
                  <Text 
                    fontSize={{ base: "xs", lg: "md" }} 
                    textAlign={{ base: "start", lg: "justify" }} 
                    lineHeight="1.6"
                  >
                    A escala vai de 1 a 5, do nível Iniciante ao Excepcional.
                  </Text>
                  
                  <Text 
                    fontSize={{ base: "xs", lg: "md" }} 
                    textAlign={{ base: "start", lg: "justify" }} 
                    lineHeight="1.6" 
                    fontWeight="medium"
                  >
                    Você está no centro da nossa estratégia de excelência.
                    Boa leitura.
                  </Text>
                </VStack>
              </VStack>
            </Flex>
          </VStack>
        </GridItem>
        
        <GridItem>
          <Box
            bgImage="url(/images/login/bg-02.png)"
            w={{ base: "100vw", lg: "100vh" }}
            h="100vh"
            rounded={{ base: "xl", lg: "none" }}
            bgRepeat="no-repeat"
            bgSize={{ base: "100% auto", lg: "cover" }}
            position="relative"
            _after={{
              content: '""',
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background:
                "linear-gradient(to right, rgba(35,34,34,1) 0%, transparent 3%)",
            }}
          />
        </GridItem>
      </Grid>
    </Flex>
  );
}
